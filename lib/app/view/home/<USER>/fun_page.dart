import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_Collection_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/fun_activity_tab_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/meituan_group_item_widget.dart';

import '../../../../common/widgets/delegate/persistent_builder.dart';
import '../../../../widgets/refresh/refresh_container.dart';
import '../../../provider/fun/fun_list_provider.dart';
import '../../../repository/modals/fun/fun_tab_config.dart';

/// 重构后的 FunPage - 使用纯 Riverpod 状态管理
class FunPage extends ConsumerWidget {
  const FunPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      body: Stack(
        children: [
          // 背景图片
          Positioned(
            child: Image.network(
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_bg.png',
              width: double.infinity,
              height: 370,
              fit: BoxFit.cover,
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: _buildDynamicListView(ref),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _emptyWidget() {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        "暂无数据",
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.network(
            "https://alicdn.msmds.cn/APPSHOW/fun_activity_title_new.png",
            width: 131,
          ),
          Row(
            children: [
              Image.network(
                "https://alicdn.msmds.cn/APPSHOW/fun_activity_location.png",
                width: 11,
              ),
              const SizedBox(width: 5),
              const Text(
                "附近",
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderComponents() {
    return Column(
      children: [
        // 视频教程组件
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          alignment: Alignment.center,
          child: _buildVideoTutorial(),
        ),
        // 返现平台视图
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: const MallCollectionWidget(),
        ),
      ],
    );
  }

  Widget _buildVideoTutorial() {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Image.network(
            'https://alicdn.msmds.cn/APPSHOW/fun_activity_step.png',
            fit: BoxFit.contain,
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: GestureDetector(
            onTap: () {
              // TODO: 导航到介绍页面
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(
                    'https://alicdn.msmds.cn/APPSHOW/fun_activity_play_tag.png',
                    width: 14,
                    height: 14,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(width: 3),
                  const Text(
                    '视频教程',
                    style: TextStyle(color: Colors.black, fontSize: 11),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建动态列表视图
  Widget _buildDynamicListView( WidgetRef ref) {
    final tabConfigState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);
    return tabConfigState.when(
      data: (tabConfig) {
        if (tabConfig == null) {
          return CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.noMore,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: _buildErrorWidget('配置加载失败'),
          );
        }

        final mainTabIndex = tabSelection.mainTabIndex;
        final childTabIndex = tabSelection.childTabIndex;

        // 验证索引有效性
        if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
            childTabIndex >= tabConfig.childrenTab.tabs.length) {
          return CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.noMore,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: _buildErrorWidget('标签配置错误'),
          );
        }

        final mainTab = tabConfig.mainTab.tabs[mainTabIndex];
        final childTab = tabConfig.childrenTab.tabs[childTabIndex];
        final currentSort = tabConfig.currentSort;

        return _buildListViewByTabType(
          mainTab.code ?? '',
          mainTab,
          childTab,
          currentSort,
        );
      },
      loading: () => CustomListView(
        sliverHeader: _buildSliverHeaders(),
        data: const <String>[],
        footerState: LoadState.loading,
        renderItem: (context, index, item) => const SizedBox.shrink(),
        empty: Container(
          margin: EdgeInsets.only(top: 80.h),
          child: const Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stackTrace) => CustomListView(
        sliverHeader: _buildSliverHeaders(),
        data: const <String>[],
        footerState: LoadState.fail,
        renderItem: (context, index, item) => const SizedBox.shrink(),
        empty: _buildErrorWidgetWithRetry(ref),
      ),
    );
  }

  /// 构建 Sliver Headers
  List<Widget> _buildSliverHeaders() {
    return [
      // 头部组件
      SliverToBoxAdapter(
        child: _buildHeaderComponents(),
      ),
      // 标签页组件
      SliverPersistentHeader(
        pinned: true,
        delegate: PersistentBuilder(
          max: 200.h,
          min: 200.h,
          builder: (_, offset) {
            return const FunActivityTabWidget();
          },
        ),
      ),
    ];
  }

  /// 根据标签类型构建对应的 CustomListView
  Widget _buildListViewByTabType(
    String tabCode,
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) {
    switch (tabCode) {
      case 'meituan_groupBuying':
      case 'meituan_sharpshooter':
        return _buildMeituanListView(mainTab, childTab, currentSort);
      case 'douyin_groupBuying':
        return _buildPlaceholderListView('抖音团购功能开发中...');
      case 'bawangcan_sort':
        return _buildPlaceholderListView('霸王餐功能开发中...');
      default:
        return _buildPlaceholderListView('请选择标签查看内容');
    }
  }

  /// 构建美团列表视图
  Widget _buildMeituanListView(
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) {
    return Consumer(
      builder: (context, ref, child) {
        final meituanProvider = fetchMeituanCouponListProvider(
          mainTab,
          childTab,
          latitude: 39.9042, // TODO: 使用真实定位
          longitude: 116.4074,
          sortOption: currentSort,
        );

        final meituanState = ref.watch(meituanProvider);

        return meituanState.when(
          data: (response) {
            final items = response.data?.list ?? [];
            if (items.isEmpty) {
              return CustomListView(
                sliverHeader: _buildSliverHeaders(),
                data: const <String>[],
                footerState: LoadState.noMore,
                renderItem: (context, index, item) => const SizedBox.shrink(),
                empty: _emptyWidget(),
              );
            }

            return CustomListView(
              sliverHeader: _buildSliverHeaders(),
              data: items,
              footerState: response.data?.hasMore == true
                  ? LoadState.idle
                  : LoadState.noMore,
              renderItem: (context, index, item) {
                return MeituanGroupItemWidget(
                  item: item,
                  index: index,
                  onShareTap: () => _handleMeituanShare(item),
                  onBuyTap: () => _handleMeituanBuy(item),
                );
              },
              empty: _emptyWidget(),
              onLoadMore: response.data?.hasMore == true
                  ? () => _loadMoreMeituanData(ref, mainTab, childTab, currentSort)
                  : null,
            );
          },
          loading: () => CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.loading,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: Container(
              margin: EdgeInsets.only(top: 80.h),
              child: const Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stackTrace) => CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.fail,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: _buildErrorWidgetWithRetry(ref),
          ),
        );
      },
    );
  }

  /// 构建占位符列表视图
  Widget _buildPlaceholderListView(String message) {
    return CustomListView(
      sliverHeader: _buildSliverHeaders(),
      data: const <String>[],
      footerState: LoadState.noMore,
      renderItem: (context, index, item) => const SizedBox.shrink(),
      empty: _buildPlaceholderContent(message),
    );
  }

  /// 构建错误提示组件
  Widget _buildErrorWidget(String message) {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        message,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  /// 构建带重试按钮的错误组件
  Widget _buildErrorWidgetWithRetry(WidgetRef ref) {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF999999),
            ),
          ),
          SizedBox(height: 8.h),
          TextButton(
            onPressed: () => ref.invalidate(manageFunTabConfigProvider),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 加载更多美团数据
  Future<void> _loadMoreMeituanData(
    WidgetRef ref,
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) async {
    // TODO: 实现分页加载逻辑
    // 这里需要扩展 fetchMeituanCouponListProvider 支持分页
    debugPrint('加载更多美团数据');
  }

  /// 处理美团分享
  void _handleMeituanShare(dynamic item) {
    // TODO: 实现分享逻辑
    debugPrint('分享美团商品: ${item.goodsName}');
  }

  /// 处理美团购买
  void _handleMeituanBuy(dynamic item) {
    // TODO: 实现购买逻辑
    debugPrint('购买美团商品: ${item.goodsName}');
  }

  Widget _buildPlaceholderContent(String text) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, color: Colors.grey),
        ),
      ),
    );
  }
}
